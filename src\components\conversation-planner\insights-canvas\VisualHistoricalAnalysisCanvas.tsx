
import React, { useState } from "react";
import { CanvasToolbar } from "./CanvasToolbar";
import { EmptyCanvasState } from "./EmptyCanvasState";
import { AICanvasControls } from "./AICanvasControls";
import { AIOverlays } from "./AIOverlays";
import { FabricCanvas } from "./FabricCanvas";
import { CanvasActionMenu } from "./CanvasActionMenu";
import { useVisualCanvas } from "./hooks/useVisualCanvas";
import { FigmaCanvasContainer } from "@/components/figma-canvas/FigmaCanvasContainer";
import { IntegrationToolbar } from "@/components/figma-canvas/compatibility/VisualCanvasIntegration";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Palette, Network } from "lucide-react";

export const VisualHistoricalAnalysisCanvas: React.FC = () => {
  const visualCanvas = useVisualCanvas();
  const [activeTab, setActiveTab] = useState<'analysis' | 'design'>('analysis');

  if (visualCanvas.allNotesCount === 0 && activeTab === 'analysis') {
    return (
      <div className="h-[700px] flex flex-col bg-background border dark:border-slate-800 rounded-lg overflow-hidden">
        <div className="p-4 border-b dark:border-slate-800">
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'analysis' | 'design')}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="analysis" className="flex items-center gap-2">
                <Network className="w-4 h-4" />
                Analysis Canvas
              </TabsTrigger>
              <TabsTrigger value="design" className="flex items-center gap-2">
                <Palette className="w-4 h-4" />
                Design Canvas
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
        <div className="flex-1">
          {activeTab === 'analysis' ? (
            <EmptyCanvasState />
          ) : (
            <div className="h-full flex flex-col">
              <div className="p-2 border-b border-gray-200">
                <IntegrationToolbar canvas={null} />
              </div>
              <div className="flex-1">
                <FigmaCanvasContainer
                  className="h-full"
                  showLayersPanel={true}
                  showPropertiesPanel={true}
                  showTransformControls={true}
                />
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }

  // Dummy zoom and pan for toolbar and overlays until they are refactored
  const zoom = 1;
  const pan = { x: 0, y: 0 };

  return (
    <div className="h-[700px] flex flex-col bg-background border dark:border-slate-800 rounded-lg overflow-hidden">
      {/* Tab Navigation */}
      <div className="border-b dark:border-slate-800">
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'analysis' | 'design')}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="analysis" className="flex items-center gap-2">
              <Network className="w-4 h-4" />
              Analysis Canvas
            </TabsTrigger>
            <TabsTrigger value="design" className="flex items-center gap-2">
              <Palette className="w-4 h-4" />
              Design Canvas
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Canvas Content */}
      <div className="flex-1 flex flex-col">
        {activeTab === 'analysis' ? (
          <>
            <CanvasToolbar
        zoom={zoom}
        onZoomIn={() => {}}
        onZoomOut={() => {}}
        onResetView={() => {}}
        selectedNodesCount={visualCanvas.selectedNodes.length}
        onConnectNodes={visualCanvas.handleConnectNodesWithToast}
        snapToGrid={visualCanvas.snapToGrid}
        onToggleSnapToGrid={() => visualCanvas.setSnapToGrid(prev => !prev)}
        searchQuery={visualCanvas.searchQuery}
        onSearchQueryChange={visualCanvas.setSearchQuery}
        analysisTypeFilter={visualCanvas.analysisTypeFilter}
        onAnalysisTypeFilterChange={visualCanvas.setAnalysisTypeFilter}
        allTags={visualCanvas.allTags}
        tagFilter={visualCanvas.tagFilter}
        onTagFilterChange={visualCanvas.setTagFilter}
        areSelectedNodesConnected={visualCanvas.areSelectedNodesConnected}
      />
      
      <div className="px-4 py-2 border-b dark:border-slate-800">
        <AICanvasControls
          isAIEnabled={visualCanvas.isAIEnabled}
          showSuggestions={visualCanvas.showSuggestions}
          showClusters={visualCanvas.showClusters}
          showPatterns={visualCanvas.showPatterns}
          showInsights={visualCanvas.showInsights}
          showExport={visualCanvas.showExport}
          isAnalyzing={visualCanvas.isAnalyzing}
          onToggleSuggestions={visualCanvas.toggleSuggestions}
          onToggleClusters={visualCanvas.toggleClusters}
          onTogglePatterns={visualCanvas.togglePatterns}
          onToggleInsights={visualCanvas.toggleInsights}
          onToggleExport={visualCanvas.toggleExport}
          onRunAnalysis={visualCanvas.handleRunAIAnalysis}
          onClearAIData={visualCanvas.clearAIData}
        />
      </div>
      
      <div className="flex-1 relative">
        <FabricCanvas
          notes={visualCanvas.notes}
          connections={visualCanvas.connections}
          nodePositions={visualCanvas.nodePositions}
          selectedNodes={visualCanvas.selectedNodes}
          connectingNodeId={visualCanvas.connectingNodeId}
          onNodeMove={visualCanvas.handleNodesMove}
          onSelectionChange={visualCanvas.handleSelectionChange}
          onNodeDoubleClick={visualCanvas.handleNodeDoubleClick}
          onCanvasClick={() => {
            visualCanvas.handleSelectionChange([], false);
            visualCanvas.closeContextMenu();
          }}
          onConnectStart={visualCanvas.setConnectingNodeId}
          onConnectEnd={visualCanvas.handleConnectEnd}
          onConnectCancel={visualCanvas.handleConnectCancel}
          onContextMenu={visualCanvas.handleContextMenu}
        />
        
        {visualCanvas.contextMenu.visible && (
          <CanvasActionMenu
            x={visualCanvas.contextMenu.x}
            y={visualCanvas.contextMenu.y}
            targetId={visualCanvas.contextMenu.targetId}
            selectedNodes={visualCanvas.selectedNodes}
            areSelectedNodesConnected={visualCanvas.areSelectedNodesConnected}
            onClose={visualCanvas.closeContextMenu}
            onDelete={(nodeIds) => {
              nodeIds.forEach(id => visualCanvas.handleDeleteNote(id));
            }}
            onToggleConnection={() => {
              const result = visualCanvas.handleToggleConnection();
              if (result && result.success) {
                // Optionally show a toast notification here
              }
            }}
            onConnectStart={visualCanvas.setConnectingNodeId}
            onViewDetails={visualCanvas.handleViewDetails}
          />
        )}
        
        <AIOverlays
          notes={visualCanvas.notes}
          connections={visualCanvas.connections}
          smartClusters={visualCanvas.smartClusters}
          patternOverlays={visualCanvas.patternOverlays}
          insightSummary={visualCanvas.insightSummary}
          connectionSuggestions={visualCanvas.connectionSuggestions}
          showClusters={visualCanvas.showClusters}
          showPatterns={visualCanvas.showPatterns}
          showSuggestions={visualCanvas.showSuggestions}
          showInsights={visualCanvas.showInsights}
          showExport={visualCanvas.showExport}
          zoom={zoom}
          pan={pan}
          onAcceptSuggestion={visualCanvas.handleAcceptSuggestion}
          onRejectSuggestion={visualCanvas.rejectSuggestion}
          toggleInsights={visualCanvas.toggleInsights}
          toggleExport={visualCanvas.toggleExport}
        />
            </div>
          </>
        ) : (
          <div className="h-full flex flex-col">
            <div className="p-2 border-b border-gray-200">
              <IntegrationToolbar canvas={null} />
            </div>
            <div className="flex-1">
              <FigmaCanvasContainer
                className="h-full"
                showLayersPanel={true}
                showPropertiesPanel={true}
                showTransformControls={true}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
